#!/bin/bash

echo "=== Демонстрация новой функциональности циклического прогрева ==="
echo ""

echo "1. Проверка справки по новым параметрам:"
echo "   ./frontrunner -help | grep progrev"
./frontrunner -help | grep progrev
echo ""

echo "2. Пример запуска с одноразовым прогревом:"
echo "   ./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -qty_progrev_usdt 15 -progrev_loop=false"
echo ""

echo "3. Пример запуска с циклическим прогревом каждые 10 секунд (по умолчанию):"
echo "   ./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -qty_progrev_usdt 15 -progrev_interval=10"
echo ""

echo "4. Пример запуска с циклическим прогревом каждые 5 секунд (по умолчанию):"
echo "   ./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -qty_progrev_usdt 15"
echo ""

echo "=== Новые параметры ==="
echo "- progrev_loop: включает циклический режим прогревочного ордера"
echo "- progrev_interval: интервал между прогревочными ордерами в секундах (по умолчанию 5)"
echo ""

echo "=== Логика работы ==="
echo "- Если progrev_loop=false: прогревочный ордер выполняется один раз перед основной логикой"
echo "- Если progrev_loop=true (по умолчанию): первый прогревочный ордер выполняется сразу, затем каждые N секунд в фоновом режиме"
echo ""

echo "Демонстрация завершена!"
