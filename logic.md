# Анализ Frontrunner Bybit

## Общее описание
Это HFT (High-Frequency Trading) бот для биржи Bybit, который реализует стратегию фронтраннинга крупных ордеров. Бот отслеживает ордербук в реальном времени и реагирует на появление крупных заявок.

## Основные компоненты

### Конфигурация
- Поддержка конфигурации через переменные окружения и флаги командной строки
- Основные параметры:
  - `symbol` - торговая пара
  - `qtyUsdt` - объем торговли в USDT
  - `qtyProgrevUsdt` - объем прогревочного ордера
  - `progrevPercent` - процент отступа для прогревочного ордера
  - `volPerelivashaUsdt` - пороговый объем для детекции крупного ордера
  - `multiplierPerelivasha` - множитель для порогового объема
  - `shagCeni` - шаг цены
  - `shagCeniMultiplier` - множитель шага цены

### Торговая логика
- Подключение к WebSocket API Bybit
- Мониторинг ордербука в реальном времени
- Реакция на крупные ордера
- Система прогревочных ордеров
- Чередование buy/sell операций

## Анализ логики lastOrderType

### Текущая реализация
1. lastOrderType == "" (пустая строка)
   - Пробуем разместить и Buy и Sell ордер (что первым сработает)
   - Это начальное состояние

2. lastOrderType != "Buy"
   - Пробуем разместить только Buy ордер
   - Это происходит после размещения Sell ордера

3. lastOrderType == "Buy"
   - Пробуем разместить только Sell ордер
   - Это происходит после размещения Buy ордера

### Потенциальные улучшения
1. Возможно стоит добавить явную проверку на "Sell" вместо != "Buy"
2. Добавить валидацию значений lastOrderType
3. Добавить логирование изменений lastOrderType
4. Рассмотреть использование enum вместо строк

### Вопросы для обсуждения
1. Нужно ли добавить защиту от одновременного размещения ордеров?
2. Стоит ли добавить таймаут между ордерами?
3. Как обрабатывать ошибки размещения ордеров?

### Анализ поведения lastOrderType == "Sell"

Текущая логика:
```go
if lastOderType != "Buy" {
    // Пробуем разместить Buy ордер
}
```

Когда lastOrderType == "Sell":
1. Условие lastOrderType != "Buy" = true
2. Система пытается разместить Buy ордер
3. Это обеспечивает чередование ордеров Sell -> Buy

Особенности реализации:
1. ✅ Правильное чередование ордеров (после Sell идет Buy)
2. ⚠️ Неявная логика (использование != "Buy" вместо == "Sell")
3. 🔍 Возможность рефакторинга для большей читаемости

Обновленная предлагаемая логика:
```go
switch lastOrderType {
case "Buy":
    // Пробуем только Sell
case "Sell":
    // Пробуем только Buy
default:
    // В случае пустой строки или неожиданного значения
    // пробуем оба типа ордеров (как было в случае с пустой строкой)
    log.Printf("Используем дефолтную логику для lastOrderType: %s", lastOrderType)
    // Пробуем оба типа ордеров
}
```

Преимущества обновленной логики:
1. Более устойчивая к ошибкам - в случае неожиданных значений продолжает работать
2. Следует принципу "fail gracefully" - даже при ошибке пытается продолжить работу
3. Логирование помогает отслеживать неожиданные ситуации
4. Уменьшение дублирования кода (логика для пустой строки и default case одинакова)
5. Более простая структура условий

Поведение в разных случаях:
1. lastOrderType == "Buy" -> пробуем только Sell
2. lastOrderType == "Sell" -> пробуем только Buy
3. lastOrderType == "" -> пробуем оба типа ордеров
4. lastOrderType == любое другое значение -> пробуем оба типа ордеров + логирование

## Новые задачи по имплементации

### 1. Добавление параметра максимального отклонения
- Добавить новый параметр конфигурации `max_price_deviation` (по умолчанию 1.0%)
- Реализовать получение значения из переменных окружения и флагов командной строки
- Добавить валидацию значения (должно быть положительным числом)

### 2. Реализация глобального хранилища ордербука
- Создать структуру `OrderBook` для хранения данных:
  ```go
  type OrderBookEntry struct {
      Price  float64
      Amount float64
  }

  type OrderBook struct {
      BidsMap map[float64]float64  // price -> amount
      AsksMap map[float64]float64  // price -> amount
      BidsSorted []float64         // отсортированные цены по убыванию
      AsksSorted []float64         // отсортированные цены по возрастанию
      BestBidPrice float64
      BestAskPrice float64
      MediumPrice  float64
      BidDeviationPrice float64    // цена отклонения для bids
      AskDeviationPrice float64    // цена отклонения для asks
      LastUpdateTime time.Time
  }
  ```

### 3. Имплементация множественных подписок на WebSocket
- Реализовать подписку на три потока данных:
  1. Level 1 (глубина 1, частота 10мс)
  2. Level 50 (глубина 50, частота 20мс)
  3. Level 200 (глубина 200, частота 100мс)
- Обработка всех потоков в одном хранилище `orderbook`

### 4. Обновление логики обработки ордербука
- Реализовать функцию обновления ордербука:
  ```go
  func (ob *OrderBook) Update(data map[string]interface{}) {
      // Обновление BidsMap и AsksMap
      // Обновление отсортированных массивов
      // Пересчет ключевых цен
  }
  ```
- Добавить функции для работы с ордербуком:
  - `UpdateBidsAndAsks()` - обновление bid и ask массивов
  - `CalculatePrices()` - расчет ключевых цен
  - `TrimToDeviation()` - обрезка ордербука до пределов отклонения

### 5. Модификация основной торговой логики
- Изменить функцию `onMessage()`:
  - Определение типа сообщения (L1/L50/L200)
  - Для L1 данных:
    - Прямая проверка объема bid/ask на соответствие условию
    - Немедленное применение логики фронтраннинга при срабатывании условия
  - Для L50/L200:
    - Обновление глобального ордербука
    - Проверка объемов с учетом отклонения от средней цены

### 6. Оптимизация обработки данных
- Минимизировать количество операций в функции обработки сообщений
- Избегать создания новых объектов в hot path
- Использовать фиксированные буферы для обработки данных
- Обеспечить быструю очистку устаревших данных

### 7. Тестирование и отладка
- Написать unit-тесты для новых компонентов
- Добавить бенчмарки для критичных операций
- Реализовать логирование производительности
- Протестировать работу на различных торговых парах

### 8. Документация
- Обновить README.md с описанием новых параметров
- Добавить примеры конфигурации
- Описать алгоритм работы с множественными потоками данных
- Обновить диаграмму алгоритма работы

### Приоритеты выполнения:
1. Задачи 1-2: Базовая инфраструктура
2. Задачи 3-4: Основная функциональность
3. Задачи 5: Торговая логика
4. Задачи 6-7: Оптимизация и тестирование
5. Задача 8: Документация

