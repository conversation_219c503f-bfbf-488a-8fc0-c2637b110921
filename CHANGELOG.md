# Changelog

## Версия 0.0.25 - Адаптивный алгоритм против манипулятора

### Новые функции

#### 1. Адаптивное уменьшение MAX_PRICE_DEVIATION
- **Флаг**: `enable_max_price_deviation_reduction` (по умолчанию `true`)
- **Описание**: После исполнения ордера манипулятора бот адаптивно уменьшает границы поиска для каждой стороны ордербука отдельно
- **Логика работы**:
  - Когда манипулятор размещает большой ордер на одной стороне (bids/asks), бот реагирует и ставится перед ним
  - После этого бот ожидает ордер манипулятора на противоположной стороне
  - Когда манипулятор убирает свой ордер, соответствующая `MAX_PRICE_DEVIATION` для этой стороны уменьшается
  - Это позволяет игнорировать большие ордера дальше от ордербука, так как манипулятор ставится все ближе к срединной цене

#### 2. Управление старыми ордерами
- **Флаг**: `cancel_old_orders` (по умолчанию `"remove_after_timeout"`)
- **Возможные значения**:
  - `"remove"` - немедленно снимать старые ордера при размещении новых
  - `"keep"` - оставлять старые ордера (может быть выставлено много ордеров одновременно)
  - `"remove_after_timeout"` - снимать старые ордера через заданное время

#### 3. Настройка таймаута для снятия ордеров
- **Флаг**: `cancel_timeout_seconds` (по умолчанию `1.5`)
- **Описание**: Время в секундах, через которое будут сняты старые ордера в режиме `remove_after_timeout`

### Технические изменения

#### Новые переменные
- `maxPriceDeviationBid` - динамическое MAX_PRICE_DEVIATION для bids
- `maxPriceDeviationAsk` - динамическое MAX_PRICE_DEVIATION для asks
- `activeOrders` - список активных ордеров для отслеживания
- `lastManipulatorOrderPriceBid` - цена последнего ордера манипулятора на bid стороне
- `lastManipulatorOrderPriceAsk` - цена последнего ордера манипулятора на ask стороне

#### Новые функции
- `createSignature()` - создание подписи для API запросов
- `cancelOrder()` - снятие ордера через библиотеку bybit
- `cancelOrderDirect()` - снятие ордера через fasthttp
- `handleOldOrders()` - обработка старых ордеров согласно настройкам

#### Обновленные функции
- `updatePriceBoundaries()` - теперь использует динамические MAX_PRICE_DEVIATION для каждой стороны
- `handleBuyScenario()` - добавлена логика уменьшения MAX_PRICE_DEVIATION для bid стороны
- `handleSellScenario()` - добавлена логика уменьшения MAX_PRICE_DEVIATION для ask стороны
- `placeOrder()` - добавлено отслеживание активных ордеров и обработка старых ордеров

### Обновления в README.md
- Добавлено описание новых параметров
- Обновлена Mermaid схема алгоритма работы
- Добавлено описание адаптивного уменьшения MAX_PRICE_DEVIATION
- Добавлено описание управления старыми ордерами

### Примеры использования

```bash
# Включить адаптивное уменьшение MAX_PRICE_DEVIATION (по умолчанию включено)
./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -enable_max_price_deviation_reduction=true

# Немедленно снимать старые ордера
./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -cancel_old_orders=remove

# Снимать старые ордера через 2 секунды
./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -cancel_old_orders=remove_after_timeout -cancel_timeout_seconds=2.0

# Оставлять все ордера активными
./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -cancel_old_orders=keep
```

### Совместимость
- Все изменения обратно совместимы
- Новые функции включены по умолчанию с оптимальными настройками
- Старые команды запуска продолжают работать без изменений
