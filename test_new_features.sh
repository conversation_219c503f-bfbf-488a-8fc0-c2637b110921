#!/bin/bash

# Тестовый скрипт для проверки новых функций

echo "=== Тест новых функций frontrunner ==="
echo

echo "1. Тест с включенным уменьшением MAX_PRICE_DEVIATION (по умолчанию):"
echo "./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -enable_max_price_deviation_reduction=true"
echo

echo "2. Тест с отключенным уменьшением MAX_PRICE_DEVIATION:"
echo "./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -enable_max_price_deviation_reduction=false"
echo

echo "3. Тест с немедленным снятием старых ордеров:"
echo "./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -cancel_old_orders=remove"
echo

echo "4. Тест с сохранением старых ордеров:"
echo "./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -cancel_old_orders=keep"
echo

echo "5. Тест с снятием старых ордеров через таймаут (по умолчанию):"
echo "./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -cancel_old_orders=remove_after_timeout -cancel_timeout_seconds=2.0"
echo

echo "6. Тест с комбинацией всех новых функций:"
echo "./frontrunner -symbol BTCUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -enable_max_price_deviation_reduction=true -cancel_old_orders=remove_after_timeout -cancel_timeout_seconds=1.5"
echo

echo "=== Для запуска тестов нужно установить API_KEY и API_SECRET в .env файле ==="
